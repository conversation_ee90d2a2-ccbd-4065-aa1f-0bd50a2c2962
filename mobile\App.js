import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import FlashMessage from 'react-native-flash-message';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Screens
import SplashScreen from './src/screens/SplashScreen';
import LoginScreen from './src/screens/auth/LoginScreen';
import RegisterScreen from './src/screens/auth/RegisterScreen';
import TeacherNavigator from './src/navigation/TeacherNavigator';
import StudentNavigator from './src/navigation/StudentNavigator';

// Context
import { AuthProvider, useAuth } from './src/context/AuthContext';

// Theme
import { theme } from './src/theme/theme';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { user, isLoading, checkAuthState } = useAuth();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeApp = async () => {
      await checkAuthState();
      setIsInitializing(false);
    };

    initializeApp();
  }, []);

  if (isInitializing || isLoading) {
    return <SplashScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user ? (
          // User is signed in
          user.role === 'teacher' ? (
            <Stack.Screen name="TeacherApp" component={TeacherNavigator} />
          ) : (
            <Stack.Screen name="StudentApp" component={StudentNavigator} />
          )
        ) : (
          // User is not signed in
          <>
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const App = () => {
  return (
    <PaperProvider theme={theme}>
      <AuthProvider>
        <AppNavigator />
        <FlashMessage position="top" />
      </AuthProvider>
    </PaperProvider>
  );
};

export default App;
