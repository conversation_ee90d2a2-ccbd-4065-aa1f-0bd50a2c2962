# Student Attendance App - Installation Guide

This guide will help you set up the complete Student Attendance Mobile App system with one-click installation.

## Prerequisites

Before starting, ensure you have the following installed on your system:

### Required Software
- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **Git** - [Download here](https://git-scm.com/)

### Optional (will be installed automatically if not present)
- **MongoDB** - Database server
- **React Native CLI** - For mobile development

## Quick Start (Recommended)

### Option 1: One-Click Web Installer

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd attendance_app
   ```

2. **Install root dependencies:**
   ```bash
   npm install
   ```

3. **Start the web installer:**
   ```bash
   npm run start-web
   ```

4. **Open your browser and go to:**
   ```
   http://localhost:3001
   ```

5. **Click "Start Installation"** and wait for the automated setup to complete.

### Option 2: Automated Script Installation

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   git clone <your-repo-url>
   cd attendance_app
   ```

2. **Run the setup script:**
   ```bash
   npm run setup
   ```

3. **Follow the on-screen instructions.**

## Manual Installation

If you prefer to set up each component manually:

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Edit .env file with your configuration
# Update MongoDB URI, JWT secret, etc.

# Start the backend server
npm run dev
```

### 2. Mobile App Setup

```bash
# Navigate to mobile directory
cd mobile

# Install dependencies
npm install

# For iOS (Mac only)
cd ios && pod install && cd ..

# Start Metro bundler
npm start

# In another terminal, run the app
# For Android
npm run android

# For iOS
npm run ios
```

### 3. Web Installer Setup

```bash
# Navigate to web installer directory
cd web-installer

# Install dependencies
npm install

# Start the web application
npm start
```

## Configuration

### Environment Variables

Create a `.env` file in the `backend` directory with the following variables:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/attendance_app

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=5MB
UPLOAD_PATH=./uploads
```

### Mobile App Configuration

Update the API configuration in `mobile/src/config/api.js`:

```javascript
export const API_CONFIG = {
  BASE_URL: 'http://localhost:5000/api', // Update with your server URL
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};
```

## Database Setup

### Option 1: Local MongoDB

1. **Install MongoDB:**
   - Windows: Download from [MongoDB website](https://www.mongodb.com/try/download/community)
   - Mac: `brew install mongodb-community`
   - Linux: Follow [official guide](https://docs.mongodb.com/manual/administration/install-on-linux/)

2. **Start MongoDB service:**
   ```bash
   # Windows
   net start MongoDB

   # Mac/Linux
   sudo systemctl start mongod
   ```

### Option 2: MongoDB Atlas (Cloud)

1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Update `MONGODB_URI` in your `.env` file

## Running the Application

### Development Mode

Start all services in development mode:

```bash
# From the root directory
npm run dev
```

This will start:
- Backend API server on `http://localhost:5000`
- Web installer on `http://localhost:3001`

### Production Mode

```bash
# Build and start all services
npm run build
npm start
```

## Mobile App Development

### Android Setup

1. **Install Android Studio** and set up Android SDK
2. **Create virtual device** or connect physical device
3. **Enable USB debugging** on physical device
4. **Run the app:**
   ```bash
   cd mobile
   npm run android
   ```

### iOS Setup (Mac only)

1. **Install Xcode** from App Store
2. **Install CocoaPods:**
   ```bash
   sudo gem install cocoapods
   ```
3. **Install iOS dependencies:**
   ```bash
   cd mobile/ios
   pod install
   cd ..
   ```
4. **Run the app:**
   ```bash
   npm run ios
   ```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Kill process using port 5000
   lsof -ti:5000 | xargs kill -9
   ```

2. **MongoDB connection error:**
   - Ensure MongoDB is running
   - Check connection string in `.env`
   - Verify network connectivity

3. **React Native build errors:**
   ```bash
   # Clean cache
   cd mobile
   npx react-native start --reset-cache
   
   # Clean build (Android)
   cd android && ./gradlew clean && cd ..
   
   # Clean build (iOS)
   cd ios && xcodebuild clean && cd ..
   ```

4. **Permission errors:**
   ```bash
   # Fix npm permissions (Linux/Mac)
   sudo chown -R $(whoami) ~/.npm
   ```

### Getting Help

- Check the logs in `installation.log`
- Review error messages in the web installer
- Ensure all prerequisites are installed
- Verify environment variables are set correctly

## Default Accounts

After installation, you can create accounts through the mobile app or use these test accounts:

### Teacher Account
- Email: `<EMAIL>`
- Password: `password123`
- Role: Teacher

### Student Account
- Email: `<EMAIL>`
- Password: `password123`
- Role: Student

## Next Steps

1. **Create your first teacher account** through the mobile app
2. **Set up subjects and classes** in the teacher dashboard
3. **Add students** to your subjects
4. **Start marking attendance** and managing events

## Support

For issues and questions:
- Check the documentation in each component's README
- Review the API documentation in `backend/README.md`
- Check the troubleshooting section above

## Security Notes

- Change default JWT secret in production
- Use HTTPS in production
- Set up proper database authentication
- Configure CORS for your domain
- Enable rate limiting and monitoring
