import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG } from '../config/api';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      await AsyncStorage.multiRemove(['token', 'user']);
      // Redirect to login screen would be handled by the auth context
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: (token) => api.get('/auth/me', {
    headers: { Authorization: `Bearer ${token}` }
  }),
  logout: () => api.post('/auth/logout'),
};

// User API
export const userAPI = {
  updateProfile: (userData) => api.put('/users/profile', userData),
  uploadProfileImage: (imageData) => {
    const formData = new FormData();
    formData.append('profileImage', imageData);
    return api.post('/users/profile-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  getUsers: (params) => api.get('/users', { params }),
  getUserById: (id) => api.get(`/users/${id}`),
};

// Attendance API
export const attendanceAPI = {
  markAttendance: (attendanceData) => api.post('/attendance', attendanceData),
  getAttendance: (params) => api.get('/attendance', { params }),
  getAttendanceByStudent: (studentId, params) => api.get(`/attendance/student/${studentId}`, { params }),
  getAttendanceBySubject: (subjectId, params) => api.get(`/attendance/subject/${subjectId}`, { params }),
  updateAttendance: (id, data) => api.put(`/attendance/${id}`, data),
  deleteAttendance: (id) => api.delete(`/attendance/${id}`),
  getAttendanceStats: (params) => api.get('/attendance/stats', { params }),
};

// Subject API
export const subjectAPI = {
  getSubjects: (params) => api.get('/subjects', { params }),
  getSubjectById: (id) => api.get(`/subjects/${id}`),
  createSubject: (subjectData) => api.post('/subjects', subjectData),
  updateSubject: (id, data) => api.put(`/subjects/${id}`, data),
  deleteSubject: (id) => api.delete(`/subjects/${id}`),
  enrollStudent: (subjectId, studentId) => api.post(`/subjects/${subjectId}/enroll`, { studentId }),
  unenrollStudent: (subjectId, studentId) => api.delete(`/subjects/${subjectId}/enroll/${studentId}`),
};

// Event API
export const eventAPI = {
  getEvents: (params) => api.get('/events', { params }),
  getEventById: (id) => api.get(`/events/${id}`),
  createEvent: (eventData) => api.post('/events', eventData),
  updateEvent: (id, data) => api.put(`/events/${id}`, data),
  deleteEvent: (id) => api.delete(`/events/${id}`),
  registerForEvent: (eventId) => api.post(`/events/${eventId}/register`),
  unregisterFromEvent: (eventId) => api.delete(`/events/${eventId}/register`),
  getMyEvents: () => api.get('/events/my-events'),
};

// Reminder API
export const reminderAPI = {
  getReminders: (params) => api.get('/reminders', { params }),
  getReminderById: (id) => api.get(`/reminders/${id}`),
  createReminder: (reminderData) => api.post('/reminders', reminderData),
  updateReminder: (id, data) => api.put(`/reminders/${id}`, data),
  deleteReminder: (id) => api.delete(`/reminders/${id}`),
  markAsRead: (id) => api.put(`/reminders/${id}/read`),
  getMyReminders: () => api.get('/reminders/my-reminders'),
};

export default api;
