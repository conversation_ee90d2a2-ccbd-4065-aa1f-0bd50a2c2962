#!/usr/bin/env node

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');

const execAsync = util.promisify(exec);

class AttendanceAppInstaller {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.logFile = path.join(this.rootDir, 'installation.log');
    this.steps = [
      { name: 'System Requirements Check', fn: this.checkSystemRequirements },
      { name: 'Environment Setup', fn: this.setupEnvironment },
      { name: 'Backend Installation', fn: this.installBackend },
      { name: 'Mobile App Setup', fn: this.setupMobileApp },
      { name: 'Web Installer Setup', fn: this.setupWebInstaller },
      { name: 'Database Initialization', fn: this.initializeDatabase },
      { name: 'Final Configuration', fn: this.finalConfiguration }
    ];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    
    console.log(logMessage);
    fs.appendFileSync(this.logFile, logMessage + '\n');
  }

  async runCommand(command, cwd = this.rootDir) {
    this.log(`Running: ${command}`);
    try {
      const { stdout, stderr } = await execAsync(command, { cwd });
      if (stdout) this.log(stdout);
      if (stderr) this.log(stderr, 'warn');
      return { success: true, stdout, stderr };
    } catch (error) {
      this.log(`Command failed: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  async checkSystemRequirements() {
    this.log('Checking system requirements...');
    
    // Check Node.js
    const nodeCheck = await this.runCommand('node --version');
    if (!nodeCheck.success) {
      throw new Error('Node.js is not installed. Please install Node.js 16 or higher.');
    }
    
    const nodeVersion = nodeCheck.stdout.trim();
    this.log(`Node.js version: ${nodeVersion}`);
    
    // Check npm
    const npmCheck = await this.runCommand('npm --version');
    if (!npmCheck.success) {
      throw new Error('npm is not installed.');
    }
    
    const npmVersion = npmCheck.stdout.trim();
    this.log(`npm version: ${npmVersion}`);
    
    // Check if MongoDB is available
    const mongoCheck = await this.runCommand('mongod --version');
    if (!mongoCheck.success) {
      this.log('MongoDB not found locally. Will use MongoDB Atlas or install locally.', 'warn');
    } else {
      this.log('MongoDB found locally');
    }
    
    // Check available ports
    const portCheck = await this.checkPorts([3000, 5000, 8081]);
    if (!portCheck.success) {
      this.log(`Port conflict detected: ${portCheck.error}`, 'warn');
    }
    
    this.log('System requirements check completed');
  }

  async checkPorts(ports) {
    // Simple port check implementation
    for (const port of ports) {
      try {
        const result = await this.runCommand(`netstat -an | grep :${port}`);
        if (result.stdout && result.stdout.includes(`${port}`)) {
          return { success: false, error: `Port ${port} is already in use` };
        }
      } catch (error) {
        // Port is likely available
      }
    }
    return { success: true };
  }

  async setupEnvironment() {
    this.log('Setting up environment files...');
    
    // Create backend .env file
    const backendEnvPath = path.join(this.rootDir, 'backend', '.env');
    const backendEnvContent = `PORT=5000
MONGODB_URI=mongodb://localhost:27017/attendance_app
JWT_SECRET=${this.generateRandomString(64)}
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
NODE_ENV=development`;
    
    fs.writeFileSync(backendEnvPath, backendEnvContent);
    this.log('Backend .env file created');
    
    // Create mobile app config
    const mobileConfigPath = path.join(this.rootDir, 'mobile', 'src', 'config');
    if (!fs.existsSync(mobileConfigPath)) {
      fs.mkdirSync(mobileConfigPath, { recursive: true });
    }
    
    const apiConfigContent = `export const API_CONFIG = {
  BASE_URL: 'http://localhost:5000/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};

export const APP_CONFIG = {
  APP_NAME: 'Attendance App',
  VERSION: '1.0.0',
  ENVIRONMENT: 'development'
};`;
    
    fs.writeFileSync(path.join(mobileConfigPath, 'api.js'), apiConfigContent);
    this.log('Mobile app configuration created');
  }

  async installBackend() {
    this.log('Installing backend dependencies...');
    
    const backendDir = path.join(this.rootDir, 'backend');
    const installResult = await this.runCommand('npm install', backendDir);
    
    if (!installResult.success) {
      throw new Error('Failed to install backend dependencies');
    }
    
    this.log('Backend dependencies installed successfully');
  }

  async setupMobileApp() {
    this.log('Setting up mobile app...');
    
    const mobileDir = path.join(this.rootDir, 'mobile');
    
    // Install React Native CLI globally if not present
    const rnCliCheck = await this.runCommand('npx react-native --version');
    if (!rnCliCheck.success) {
      this.log('Installing React Native CLI...');
      await this.runCommand('npm install -g @react-native-community/cli');
    }
    
    // Install mobile dependencies
    const installResult = await this.runCommand('npm install', mobileDir);
    if (!installResult.success) {
      throw new Error('Failed to install mobile app dependencies');
    }
    
    // Create necessary directories
    const directories = [
      'src/screens/auth',
      'src/screens/teacher',
      'src/screens/student',
      'src/navigation',
      'src/components',
      'src/services',
      'src/utils',
      'src/theme'
    ];
    
    directories.forEach(dir => {
      const fullPath = path.join(mobileDir, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        this.log(`Created directory: ${dir}`);
      }
    });
    
    this.log('Mobile app setup completed');
  }

  async setupWebInstaller() {
    this.log('Setting up web installer...');
    
    const webDir = path.join(this.rootDir, 'web-installer');
    const installResult = await this.runCommand('npm install', webDir);
    
    if (!installResult.success) {
      throw new Error('Failed to install web installer dependencies');
    }
    
    this.log('Web installer setup completed');
  }

  async initializeDatabase() {
    this.log('Initializing database...');
    
    // Check if MongoDB is running
    const mongoCheck = await this.runCommand('mongo --eval "db.runCommand({connectionStatus : 1})"');
    
    if (!mongoCheck.success) {
      this.log('Starting MongoDB service...', 'warn');
      // Try to start MongoDB service (platform-specific)
      if (process.platform === 'win32') {
        await this.runCommand('net start MongoDB');
      } else {
        await this.runCommand('sudo systemctl start mongod');
      }
    }
    
    this.log('Database initialization completed');
  }

  async finalConfiguration() {
    this.log('Performing final configuration...');
    
    // Create startup scripts
    const startupScript = `#!/bin/bash
echo "Starting Attendance App..."
echo "Starting backend server..."
cd backend && npm start &
echo "Starting web installer..."
cd web-installer && npm start &
echo "All services started!"
echo "Backend API: http://localhost:5000"
echo "Web Installer: http://localhost:3001"
echo "To start mobile app: cd mobile && npm start"`;
    
    fs.writeFileSync(path.join(this.rootDir, 'start.sh'), startupScript);
    
    // Make script executable on Unix systems
    if (process.platform !== 'win32') {
      await this.runCommand('chmod +x start.sh');
    }
    
    // Create Windows batch file
    const windowsScript = `@echo off
echo Starting Attendance App...
echo Starting backend server...
cd backend && start npm start
echo Starting web installer...
cd web-installer && start npm start
echo All services started!
echo Backend API: http://localhost:5000
echo Web Installer: http://localhost:3001
echo To start mobile app: cd mobile && npm start
pause`;
    
    fs.writeFileSync(path.join(this.rootDir, 'start.bat'), windowsScript);
    
    this.log('Final configuration completed');
  }

  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async install() {
    this.log('Starting Attendance App installation...');
    
    try {
      for (let i = 0; i < this.steps.length; i++) {
        const step = this.steps[i];
        this.log(`Step ${i + 1}/${this.steps.length}: ${step.name}`);
        await step.fn.call(this);
        this.log(`✓ ${step.name} completed`);
      }
      
      this.log('🎉 Installation completed successfully!');
      this.log('To start the application, run: npm run dev');
      this.log('Or use the startup scripts: ./start.sh (Linux/Mac) or start.bat (Windows)');
      
    } catch (error) {
      this.log(`❌ Installation failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Run installer if called directly
if (require.main === module) {
  const installer = new AttendanceAppInstaller();
  installer.install();
}

module.exports = AttendanceAppInstaller;
