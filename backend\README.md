# Attendance App Backend API

RESTful API server for the Student Attendance Mobile App built with Node.js, Express, and MongoDB.

## Features

- JWT-based authentication
- Role-based access control (Student, Teacher, Admin)
- Attendance management
- Event management
- Reminder system
- Real-time notifications with Socket.io
- File upload support
- Input validation and sanitization
- Rate limiting and security headers

## Installation

1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Update environment variables in `.env`:
```
PORT=5000
MONGODB_URI=mongodb://localhost:27017/attendance_app
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
NODE_ENV=development
```

4. Start the server:
```bash
# Development
npm run dev

# Production
npm start
```

## API Endpoints

### Authentication

#### POST /api/auth/register
Register a new user (student or teacher)

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "student",
  "department": "Computer Science",
  "semester": 5,
  "studentId": "CS2021001"
}
```

#### POST /api/auth/login
Login user

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### GET /api/auth/me
Get current user profile (requires authentication)

### Users

#### GET /api/users
Get all users (teachers only)

#### PUT /api/users/profile
Update user profile

#### POST /api/users/profile-image
Upload profile image

### Attendance

#### POST /api/attendance
Mark attendance (teachers only)

**Request Body:**
```json
{
  "student": "student_id",
  "subject": "subject_id",
  "status": "present",
  "date": "2023-10-01",
  "notes": "On time"
}
```

#### GET /api/attendance
Get attendance records

**Query Parameters:**
- `student`: Filter by student ID
- `subject`: Filter by subject ID
- `date`: Filter by date
- `status`: Filter by status

#### GET /api/attendance/student/:studentId
Get attendance for specific student

#### GET /api/attendance/subject/:subjectId
Get attendance for specific subject

#### GET /api/attendance/stats
Get attendance statistics

### Subjects

#### GET /api/subjects
Get all subjects

#### POST /api/subjects
Create new subject (teachers only)

**Request Body:**
```json
{
  "name": "Data Structures",
  "code": "CS301",
  "department": "Computer Science",
  "semester": 3,
  "credits": 4,
  "schedule": [
    {
      "day": "Monday",
      "startTime": "09:00",
      "endTime": "10:00",
      "room": "CS-101"
    }
  ]
}
```

#### PUT /api/subjects/:id
Update subject

#### DELETE /api/subjects/:id
Delete subject

#### POST /api/subjects/:id/enroll
Enroll student in subject

### Events

#### GET /api/events
Get all events

#### POST /api/events
Create new event (teachers only)

**Request Body:**
```json
{
  "title": "Tech Fest 2023",
  "description": "Annual technology festival",
  "eventDate": "2023-12-15",
  "eventTime": "10:00",
  "location": "Main Auditorium",
  "category": "cultural",
  "targetAudience": "all"
}
```

#### PUT /api/events/:id
Update event

#### DELETE /api/events/:id
Delete event

#### POST /api/events/:id/register
Register for event

### Reminders

#### GET /api/reminders
Get all reminders

#### POST /api/reminders
Create new reminder (teachers only)

**Request Body:**
```json
{
  "title": "Assignment Due",
  "message": "Submit your project report by tomorrow",
  "reminderDate": "2023-10-15",
  "reminderTime": "09:00",
  "priority": "high",
  "targetAudience": "specific_subject",
  "subject": "subject_id"
}
```

#### PUT /api/reminders/:id/read
Mark reminder as read

## Authentication

All protected routes require a JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [] // Validation errors if applicable
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Real-time Features

The server supports real-time notifications using Socket.io. Connect to the server and listen for events:

- `attendance-marked` - When attendance is marked
- `event-created` - When new event is created
- `reminder-sent` - When reminder is sent

## Testing

Run tests with:
```bash
npm test
```

## Security Features

- Helmet.js for security headers
- Rate limiting
- Input validation and sanitization
- Password hashing with bcrypt
- JWT token expiration
- CORS configuration
