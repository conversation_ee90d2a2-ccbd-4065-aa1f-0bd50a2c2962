const mongoose = require('mongoose');

const reminderSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Reminder title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  message: {
    type: String,
    required: [true, 'Reminder message is required'],
    trim: true,
    maxlength: [300, 'Message cannot be more than 300 characters']
  },
  reminderDate: {
    type: Date,
    required: [true, 'Reminder date is required']
  },
  reminderTime: {
    type: String,
    required: [true, 'Reminder time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter valid time format (HH:MM)']
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  category: {
    type: String,
    enum: ['assignment', 'exam', 'project', 'meeting', 'deadline', 'event', 'other'],
    default: 'other'
  },
  targetAudience: {
    type: String,
    enum: ['all', 'students', 'teachers', 'specific_department', 'specific_semester', 'specific_subject'],
    default: 'all'
  },
  department: {
    type: String,
    required: function() {
      return this.targetAudience === 'specific_department';
    }
  },
  semester: {
    type: Number,
    required: function() {
      return this.targetAudience === 'specific_semester';
    },
    min: 1,
    max: 8
  },
  subject: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject',
    required: function() {
      return this.targetAudience === 'specific_subject';
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isSent: {
    type: Boolean,
    default: false
  },
  sentAt: {
    type: Date,
    default: null
  },
  recipients: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    isRead: {
      type: Boolean,
      default: false
    },
    readAt: {
      type: Date,
      default: null
    }
  }],
  repeatType: {
    type: String,
    enum: ['none', 'daily', 'weekly', 'monthly'],
    default: 'none'
  },
  repeatUntil: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update updatedAt field before saving
reminderSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for efficient querying
reminderSchema.index({ reminderDate: 1, isActive: 1, isSent: 1 });
reminderSchema.index({ targetAudience: 1, department: 1, semester: 1 });

module.exports = mongoose.model('Reminder', reminderSchema);
