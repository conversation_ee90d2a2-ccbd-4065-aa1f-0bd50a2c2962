#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

class AppStarter {
  constructor() {
    this.processes = [];
    this.rootDir = __dirname;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      warning: '\x1b[33m', // Yellow
      error: '\x1b[31m',   // Red
      reset: '\x1b[0m'     // Reset
    };
    
    console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
  }

  async checkPrerequisites() {
    this.log('Checking prerequisites...', 'info');
    
    try {
      // Check Node.js
      await this.runCommand('node --version');
      this.log('✓ Node.js is installed', 'success');
      
      // Check npm
      await this.runCommand('npm --version');
      this.log('✓ npm is installed', 'success');
      
      // Check if dependencies are installed
      const backendPackageJson = path.join(this.rootDir, 'backend', 'package.json');
      const mobilePackageJson = path.join(this.rootDir, 'mobile', 'package.json');
      const webPackageJson = path.join(this.rootDir, 'web-installer', 'package.json');
      
      if (!fs.existsSync(backendPackageJson) || 
          !fs.existsSync(mobilePackageJson) || 
          !fs.existsSync(webPackageJson)) {
        throw new Error('Project structure incomplete. Please run setup first.');
      }
      
      // Check if node_modules exist
      const backendNodeModules = path.join(this.rootDir, 'backend', 'node_modules');
      const webNodeModules = path.join(this.rootDir, 'web-installer', 'node_modules');
      
      if (!fs.existsSync(backendNodeModules) || !fs.existsSync(webNodeModules)) {
        this.log('Dependencies not installed. Installing now...', 'warning');
        await this.installDependencies();
      }
      
      this.log('✓ All prerequisites met', 'success');
      
    } catch (error) {
      this.log(`✗ Prerequisite check failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async runCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve({ stdout, stderr });
        }
      });
    });
  }

  async installDependencies() {
    this.log('Installing backend dependencies...', 'info');
    await this.runCommand('npm install', { cwd: path.join(this.rootDir, 'backend') });
    
    this.log('Installing web installer dependencies...', 'info');
    await this.runCommand('npm install', { cwd: path.join(this.rootDir, 'web-installer') });
    
    this.log('✓ Dependencies installed', 'success');
  }

  startProcess(name, command, args, cwd) {
    return new Promise((resolve, reject) => {
      this.log(`Starting ${name}...`, 'info');
      
      const process = spawn(command, args, {
        cwd,
        stdio: 'pipe',
        shell: true
      });

      process.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
          this.log(`[${name}] ${output}`, 'info');
        }
      });

      process.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output && !output.includes('warning')) {
          this.log(`[${name}] ${output}`, 'warning');
        }
      });

      process.on('error', (error) => {
        this.log(`[${name}] Error: ${error.message}`, 'error');
        reject(error);
      });

      // Consider the process started after a short delay
      setTimeout(() => {
        if (!process.killed) {
          this.processes.push({ name, process });
          this.log(`✓ ${name} started successfully`, 'success');
          resolve(process);
        }
      }, 2000);
    });
  }

  async startBackend() {
    const backendDir = path.join(this.rootDir, 'backend');
    
    // Check if .env exists
    const envPath = path.join(backendDir, '.env');
    if (!fs.existsSync(envPath)) {
      this.log('Creating backend .env file...', 'info');
      const envExample = path.join(backendDir, '.env.example');
      if (fs.existsSync(envExample)) {
        fs.copyFileSync(envExample, envPath);
      }
    }
    
    return this.startProcess('Backend API', 'npm', ['run', 'dev'], backendDir);
  }

  async startWebInstaller() {
    const webDir = path.join(this.rootDir, 'web-installer');
    return this.startProcess('Web Installer', 'npm', ['start'], webDir);
  }

  async startAll() {
    try {
      this.log('🚀 Starting Student Attendance App...', 'info');
      
      await this.checkPrerequisites();
      
      // Start backend first
      await this.startBackend();
      
      // Wait a bit for backend to initialize
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Start web installer
      await this.startWebInstaller();
      
      this.log('🎉 All services started successfully!', 'success');
      this.log('', 'info');
      this.log('📱 Access your application:', 'info');
      this.log('   • Backend API: http://localhost:5000', 'info');
      this.log('   • Web Installer: http://localhost:3001', 'info');
      this.log('   • API Health Check: http://localhost:5000/health', 'info');
      this.log('', 'info');
      this.log('📋 To start mobile app:', 'info');
      this.log('   cd mobile && npm start', 'info');
      this.log('', 'info');
      this.log('Press Ctrl+C to stop all services', 'warning');
      
    } catch (error) {
      this.log(`Failed to start application: ${error.message}`, 'error');
      this.cleanup();
      process.exit(1);
    }
  }

  cleanup() {
    this.log('Stopping all services...', 'warning');
    
    this.processes.forEach(({ name, process }) => {
      if (!process.killed) {
        this.log(`Stopping ${name}...`, 'info');
        process.kill('SIGTERM');
      }
    });
    
    this.log('All services stopped', 'info');
  }

  setupSignalHandlers() {
    process.on('SIGINT', () => {
      this.log('\nReceived SIGINT, shutting down gracefully...', 'warning');
      this.cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.log('Received SIGTERM, shutting down gracefully...', 'warning');
      this.cleanup();
      process.exit(0);
    });
  }
}

// Run if called directly
if (require.main === module) {
  const starter = new AppStarter();
  starter.setupSignalHandlers();
  starter.startAll().catch(error => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}

module.exports = AppStarter;
