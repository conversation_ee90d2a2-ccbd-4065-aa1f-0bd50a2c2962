import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  <PERSON>,
  Typography,
  <PERSON><PERSON>,
  Box,
  Stepper,
  Step,
  Step<PERSON><PERSON>l,
  StepContent,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  Error,
  PhoneAndroid,
  Storage,
  Cloud,
  Settings
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import axios from 'axios';
import io from 'socket.io-client';

const steps = [
  {
    label: 'System Requirements Check',
    description: 'Checking Node.js, npm, and other dependencies',
    icon: <Settings />
  },
  {
    label: 'Database Setup',
    description: 'Installing and configuring MongoDB',
    icon: <Storage />
  },
  {
    label: 'Backend Installation',
    description: 'Installing backend dependencies and starting server',
    icon: <Cloud />
  },
  {
    label: 'Mobile App Setup',
    description: 'Setting up React Native development environment',
    icon: <PhoneAndroid />
  }
];

function App() {
  const [activeStep, setActiveStep] = useState(-1);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installationStatus, setInstallationStatus] = useState({});
  const [logs, setLogs] = useState([]);
  const [socket, setSocket] = useState(null);
  const [systemInfo, setSystemInfo] = useState(null);

  useEffect(() => {
    // Check system requirements on load
    checkSystemRequirements();
    
    // Initialize socket connection
    const newSocket = io('http://localhost:5000');
    setSocket(newSocket);
    
    newSocket.on('installation-progress', (data) => {
      setLogs(prev => [...prev, data]);
      if (data.step !== undefined) {
        setActiveStep(data.step);
      }
      if (data.status) {
        setInstallationStatus(prev => ({
          ...prev,
          [data.step]: data.status
        }));
      }
    });

    return () => newSocket.close();
  }, []);

  const checkSystemRequirements = async () => {
    try {
      const response = await axios.get('/api/installer/system-check');
      setSystemInfo(response.data);
    } catch (error) {
      console.error('System check failed:', error);
    }
  };

  const startInstallation = async () => {
    setIsInstalling(true);
    setActiveStep(0);
    setLogs([]);
    
    try {
      await axios.post('/api/installer/start');
    } catch (error) {
      console.error('Installation failed:', error);
      setIsInstalling(false);
    }
  };

  const getStepStatus = (stepIndex) => {
    const status = installationStatus[stepIndex];
    if (status === 'completed') return 'success';
    if (status === 'error') return 'error';
    if (stepIndex === activeStep) return 'progress';
    return 'pending';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          <Box textAlign="center" mb={4}>
            <Typography variant="h3" component="h1" gutterBottom color="primary">
              Student Attendance App
            </Typography>
            <Typography variant="h5" color="textSecondary" gutterBottom>
              One-Click Installer & Setup
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Install and configure the complete attendance management system with a single click
            </Typography>
          </Box>

          {/* System Information */}
          {systemInfo && (
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  System Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body2">Node.js:</Typography>
                      <Chip
                        label={systemInfo.node || 'Not Found'}
                        color={systemInfo.node ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body2">npm:</Typography>
                      <Chip
                        label={systemInfo.npm || 'Not Found'}
                        color={systemInfo.npm ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body2">MongoDB:</Typography>
                      <Chip
                        label={systemInfo.mongodb ? 'Available' : 'Will Install'}
                        color={systemInfo.mongodb ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body2">React Native CLI:</Typography>
                      <Chip
                        label={systemInfo.reactNative ? 'Available' : 'Will Install'}
                        color={systemInfo.reactNative ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}

          {/* Installation Button */}
          <Box textAlign="center" mb={4}>
            <Button
              variant="contained"
              size="large"
              startIcon={isInstalling ? <CircularProgress size={20} /> : <PlayArrow />}
              onClick={startInstallation}
              disabled={isInstalling}
              sx={{ px: 4, py: 2 }}
            >
              {isInstalling ? 'Installing...' : 'Start Installation'}
            </Button>
          </Box>

          {/* Installation Progress */}
          {isInstalling && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Stepper activeStep={activeStep} orientation="vertical">
                {steps.map((step, index) => (
                  <Step key={step.label}>
                    <StepLabel
                      StepIconComponent={() => {
                        const status = getStepStatus(index);
                        if (status === 'success') return <CheckCircle color="success" />;
                        if (status === 'error') return <Error color="error" />;
                        if (status === 'progress') return <CircularProgress size={24} />;
                        return step.icon;
                      }}
                    >
                      {step.label}
                    </StepLabel>
                    <StepContent>
                      <Typography variant="body2" color="textSecondary">
                        {step.description}
                      </Typography>
                      {index === activeStep && (
                        <Box mt={2}>
                          <LinearProgress />
                        </Box>
                      )}
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </motion.div>
          )}

          {/* Installation Logs */}
          {logs.length > 0 && (
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Installation Logs
                </Typography>
                <Box
                  sx={{
                    maxHeight: 300,
                    overflow: 'auto',
                    bgcolor: 'grey.100',
                    p: 2,
                    borderRadius: 1,
                    fontFamily: 'monospace'
                  }}
                >
                  {logs.map((log, index) => (
                    <Typography
                      key={index}
                      variant="body2"
                      sx={{
                        color: log.type === 'error' ? 'error.main' : 'text.primary',
                        mb: 0.5
                      }}
                    >
                      [{new Date(log.timestamp).toLocaleTimeString()}] {log.message}
                    </Typography>
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Success Message */}
          {installationStatus[steps.length - 1] === 'completed' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Alert severity="success" sx={{ mt: 4 }}>
                <Typography variant="h6">Installation Completed Successfully!</Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Your Student Attendance App is now ready to use:
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    • Backend API: <strong>http://localhost:5000</strong>
                  </Typography>
                  <Typography variant="body2">
                    • Mobile App: Run <code>npm run start-mobile</code> to start the React Native app
                  </Typography>
                  <Typography variant="body2">
                    • Admin Panel: <strong>http://localhost:3000</strong>
                  </Typography>
                </Box>
              </Alert>
            </motion.div>
          )}
        </Paper>
      </motion.div>
    </Container>
  );
}

export default App;
