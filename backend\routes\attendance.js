const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Attendance = require('../models/Attendance');
const User = require('../models/User');
const Subject = require('../models/Subject');
const { auth, teacherAuth } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/attendance
// @desc    Mark attendance
// @access  Private (Teachers only)
router.post('/', teacherAuth, [
  body('student').isMongoId().withMessage('Valid student ID is required'),
  body('subject').isMongoId().withMessage('Valid subject ID is required'),
  body('status').isIn(['present', 'absent', 'late']).withMessage('Status must be present, absent, or late'),
  body('date').isISO8601().withMessage('Valid date is required'),
  body('notes').optional().isLength({ max: 200 }).withMessage('Notes cannot exceed 200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { student, subject, status, date, notes, timeIn, timeOut, location } = req.body;

    // Verify student exists
    const studentUser = await User.findById(student);
    if (!studentUser || studentUser.role !== 'student') {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Verify subject exists and teacher has access
    const subjectDoc = await Subject.findById(subject);
    if (!subjectDoc) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    if (subjectDoc.teacher.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only mark attendance for your subjects'
      });
    }

    // Check if attendance already exists for this date
    const existingAttendance = await Attendance.findOne({
      student,
      subject,
      date: new Date(date)
    });

    if (existingAttendance) {
      return res.status(400).json({
        success: false,
        message: 'Attendance already marked for this date'
      });
    }

    // Create attendance record
    const attendance = new Attendance({
      student,
      subject,
      teacher: req.user._id,
      status,
      date: new Date(date),
      notes,
      timeIn: timeIn ? new Date(timeIn) : null,
      timeOut: timeOut ? new Date(timeOut) : null,
      location
    });

    await attendance.save();

    // Populate the response
    await attendance.populate([
      { path: 'student', select: 'name email studentId' },
      { path: 'subject', select: 'name code' },
      { path: 'teacher', select: 'name email' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Attendance marked successfully',
      attendance
    });

  } catch (error) {
    console.error('Mark attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/attendance
// @desc    Get attendance records
// @access  Private
router.get('/', auth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('student').optional().isMongoId().withMessage('Valid student ID required'),
  query('subject').optional().isMongoId().withMessage('Valid subject ID required'),
  query('status').optional().isIn(['present', 'absent', 'late']).withMessage('Invalid status'),
  query('startDate').optional().isISO8601().withMessage('Valid start date required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      student,
      subject,
      status,
      startDate,
      endDate,
      sortBy = 'date',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};

    // Role-based filtering
    if (req.user.role === 'student') {
      query.student = req.user._id;
    } else if (req.user.role === 'teacher') {
      // Teachers can only see attendance for their subjects
      const teacherSubjects = await Subject.find({ teacher: req.user._id }).select('_id');
      query.subject = { $in: teacherSubjects.map(s => s._id) };
    }

    // Apply filters
    if (student) query.student = student;
    if (subject) query.subject = subject;
    if (status) query.status = status;
    
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [attendance, total] = await Promise.all([
      Attendance.find(query)
        .populate('student', 'name email studentId department semester')
        .populate('subject', 'name code department')
        .populate('teacher', 'name email')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      Attendance.countDocuments(query)
    ]);

    res.json({
      success: true,
      attendance,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
        totalRecords: total,
        hasNext: skip + parseInt(limit) < total,
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/attendance/student/:studentId
// @desc    Get attendance for specific student
// @access  Private
router.get('/student/:studentId', auth, async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subject, startDate, endDate } = req.query;

    // Authorization check
    if (req.user.role === 'student' && req.user._id.toString() !== studentId) {
      return res.status(403).json({
        success: false,
        message: 'You can only view your own attendance'
      });
    }

    const query = { student: studentId };
    if (subject) query.subject = subject;
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    const attendance = await Attendance.find(query)
      .populate('subject', 'name code')
      .populate('teacher', 'name')
      .sort({ date: -1 });

    // Calculate statistics
    const stats = {
      total: attendance.length,
      present: attendance.filter(a => a.status === 'present').length,
      absent: attendance.filter(a => a.status === 'absent').length,
      late: attendance.filter(a => a.status === 'late').length
    };

    stats.percentage = stats.total > 0 ? ((stats.present + stats.late) / stats.total * 100).toFixed(2) : 0;

    res.json({
      success: true,
      attendance,
      stats
    });

  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/attendance/:id
// @desc    Update attendance record
// @access  Private (Teachers only)
router.put('/:id', teacherAuth, [
  body('status').optional().isIn(['present', 'absent', 'late']).withMessage('Status must be present, absent, or late'),
  body('notes').optional().isLength({ max: 200 }).withMessage('Notes cannot exceed 200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const attendance = await Attendance.findById(req.params.id);
    if (!attendance) {
      return res.status(404).json({
        success: false,
        message: 'Attendance record not found'
      });
    }

    // Check if teacher owns this attendance record
    if (attendance.teacher.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own attendance records'
      });
    }

    // Update fields
    const { status, notes, timeIn, timeOut } = req.body;
    if (status) attendance.status = status;
    if (notes !== undefined) attendance.notes = notes;
    if (timeIn) attendance.timeIn = new Date(timeIn);
    if (timeOut) attendance.timeOut = new Date(timeOut);

    await attendance.save();

    await attendance.populate([
      { path: 'student', select: 'name email studentId' },
      { path: 'subject', select: 'name code' }
    ]);

    res.json({
      success: true,
      message: 'Attendance updated successfully',
      attendance
    });

  } catch (error) {
    console.error('Update attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/attendance/stats
// @desc    Get attendance statistics
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    const { subject, startDate, endDate } = req.query;

    const matchStage = {};
    
    // Role-based filtering
    if (req.user.role === 'student') {
      matchStage.student = req.user._id;
    } else if (req.user.role === 'teacher') {
      const teacherSubjects = await Subject.find({ teacher: req.user._id }).select('_id');
      matchStage.subject = { $in: teacherSubjects.map(s => s._id) };
    }

    if (subject) matchStage.subject = subject;
    if (startDate || endDate) {
      matchStage.date = {};
      if (startDate) matchStage.date.$gte = new Date(startDate);
      if (endDate) matchStage.date.$lte = new Date(endDate);
    }

    const stats = await Attendance.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const result = {
      present: 0,
      absent: 0,
      late: 0,
      total: 0
    };

    stats.forEach(stat => {
      result[stat._id] = stat.count;
      result.total += stat.count;
    });

    result.percentage = result.total > 0 ? ((result.present + result.late) / result.total * 100).toFixed(2) : 0;

    res.json({
      success: true,
      stats: result
    });

  } catch (error) {
    console.error('Get attendance stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
