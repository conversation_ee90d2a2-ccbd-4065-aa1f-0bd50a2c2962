{"name": "attendance-app-installer", "version": "1.0.0", "description": "Web installer for Student Attendance Mobile App", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.5.0", "@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "socket.io-client": "^4.7.2", "react-router-dom": "^6.15.0", "framer-motion": "^10.16.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}