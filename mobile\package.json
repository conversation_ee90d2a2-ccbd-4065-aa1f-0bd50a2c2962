{"name": "AttendanceApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.4", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-screens": "^3.22.1", "react-native-safe-area-context": "^4.7.1", "react-native-gesture-handler": "^2.12.1", "react-native-reanimated": "^3.4.2", "react-native-vector-icons": "^10.0.0", "@react-native-async-storage/async-storage": "^1.19.1", "react-native-paper": "^5.10.1", "react-native-elements": "^3.4.3", "axios": "^1.5.0", "react-native-image-picker": "^5.6.0", "react-native-permissions": "^3.8.4", "react-native-geolocation-service": "^5.3.1", "react-native-push-notification": "^8.1.1", "react-native-date-picker": "^4.3.3", "react-native-calendars": "^1.1301.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.13.0", "socket.io-client": "^4.7.2", "react-hook-form": "^7.45.4", "react-native-flash-message": "^0.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}